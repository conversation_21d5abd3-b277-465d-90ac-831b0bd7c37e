const http = require('http');

class DebugSSEClient {
    constructor() {
        this.sessionId = null;
        this.messageId = 1;
    }

    async connect() {
        return new Promise((resolve, reject) => {
            console.log('🔗 Connecting to SSE...');
            
            const options = {
                hostname: 'localhost',
                port: 3333,
                path: '/sse',
                method: 'GET',
                headers: {
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive'
                }
            };

            const req = http.request(options, (res) => {
                console.log('✅ SSE Connected, status:', res.statusCode);
                
                let buffer = '';
                res.on('data', (chunk) => {
                    buffer += chunk.toString();
                    console.log('📥 Raw SSE data:', JSON.stringify(chunk.toString()));
                    
                    // Process complete messages
                    const lines = buffer.split('\n');
                    buffer = lines.pop(); // Keep incomplete line in buffer
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const dataContent = line.substring(6);
                            console.log('📋 SSE data content:', dataContent);
                            
                            try {
                                const eventData = JSON.parse(dataContent);
                                console.log('✅ Parsed JSON:', JSON.stringify(eventData, null, 2));
                            } catch (e) {
                                if (dataContent.includes('sessionId=')) {
                                    const match = dataContent.match(/sessionId=([a-f0-9-]+)/);
                                    if (match) {
                                        this.sessionId = match[1];
                                        console.log('🔑 Session ID extracted:', this.sessionId);
                                    }
                                } else {
                                    console.log('📝 Non-JSON data:', dataContent);
                                }
                            }
                        } else if (line.startsWith('event: ')) {
                            console.log('🎯 Event type:', line.substring(7));
                        } else if (line.trim()) {
                            console.log('📄 Other line:', line);
                        }
                    }
                });

                res.on('end', () => {
                    console.log('❌ SSE connection ended');
                });

                res.on('error', (err) => {
                    console.error('❌ SSE error:', err);
                    reject(err);
                });

                resolve();
            });

            req.on('error', (err) => {
                console.error('❌ Connection error:', err);
                reject(err);
            });

            req.setTimeout(5000, () => {
                reject(new Error('Connection timeout'));
            });

            req.end();
        });
    }

    async sendMessage(method, params = {}) {
        if (!this.sessionId) {
            throw new Error('No session established');
        }

        const message = {
            jsonrpc: '2.0',
            id: this.messageId++,
            method: method,
            params: params
        };

        console.log('📤 Sending message:', JSON.stringify(message, null, 2));

        const data = JSON.stringify(message);
        const options = {
            hostname: 'localhost',
            port: 3333,
            path: `/messages?sessionId=${this.sessionId}`,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(data)
            }
        };

        return new Promise((resolve, reject) => {
            const req = http.request(options, (res) => {
                let body = '';
                res.on('data', (chunk) => body += chunk);
                res.on('end', () => {
                    console.log('📨 POST Response status:', res.statusCode);
                    console.log('📨 POST Response body:', body);
                    resolve(body);
                });
            });

            req.on('error', (err) => {
                console.error('❌ POST error:', err);
                reject(err);
            });

            req.write(data);
            req.end();
        });
    }
}

// Test the debug client
async function main() {
    const client = new DebugSSEClient();

    try {
        await client.connect();
        
        // Wait for session to be established
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        if (!client.sessionId) {
            console.log('❌ No session ID received');
            return;
        }

        console.log('\n🚀 Testing initialize...');
        await client.sendMessage('initialize', {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: { name: 'debug-client', version: '1.0.0' }
        });

        // Wait for response
        await new Promise(resolve => setTimeout(resolve, 5000));

        console.log('\n🔧 Testing get_figma_data...');
        await client.sendMessage('tools/call', {
            name: 'get_figma_data',
            arguments: { fileKey: 'Py8RFAyWK8jUklLH9fqHvo' }
        });

        // Wait for response
        await new Promise(resolve => setTimeout(resolve, 30000));

    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

main();
