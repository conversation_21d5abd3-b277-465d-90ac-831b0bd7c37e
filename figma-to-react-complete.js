const http = require('http');
const readline = require('readline');
const fs = require('fs');
const path = require('path');

class FigmaToReactConverter {
    constructor(apiKey, port = 3333) {
        this.apiKey = apiKey;
        this.port = port;
        this.messageId = 1;
        this.sessionId = null;
        this.sseConnection = null;
        this.pendingRequests = new Map();
    }

    async connect() {
        return new Promise((resolve, reject) => {
            console.log('🔗 Connecting to Figma MCP Server...');
            
            const options = {
                hostname: 'localhost',
                port: this.port,
                path: '/sse',
                method: 'GET',
                headers: {
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive'
                }
            };

            const req = http.request(options, (res) => {
                this.sseConnection = res;
                
                res.on('data', (chunk) => {
                    const data = chunk.toString();
                    const lines = data.split('\n');
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const dataContent = line.substring(6);
                            try {
                                const eventData = JSON.parse(dataContent);
                                this.handleMessage(eventData);
                            } catch (e) {
                                if (dataContent.includes('sessionId=')) {
                                    const match = dataContent.match(/sessionId=([a-f0-9-]+)/);
                                    if (match) {
                                        this.sessionId = match[1];
                                        console.log('✅ Session established:', this.sessionId);
                                    }
                                }
                            }
                        }
                    }
                });

                res.on('error', (err) => reject(err));
                resolve();
            });

            req.on('error', (err) => reject(err));
            req.setTimeout(5000, () => reject(new Error('Connection timeout')));
            req.end();
        });
    }

    handleMessage(message) {
        if (message.id && this.pendingRequests.has(message.id)) {
            const { resolve, reject } = this.pendingRequests.get(message.id);
            this.pendingRequests.delete(message.id);
            
            if (message.error) {
                reject(new Error(message.error.message || 'Unknown error'));
            } else {
                resolve(message.result);
            }
        }
    }

    async sendMessage(method, params = {}) {
        if (!this.sessionId) {
            throw new Error('No session established');
        }

        const message = {
            jsonrpc: '2.0',
            id: this.messageId++,
            method: method,
            params: params
        };

        return new Promise((resolve, reject) => {
            this.pendingRequests.set(message.id, { resolve, reject });
            
            setTimeout(() => {
                if (this.pendingRequests.has(message.id)) {
                    this.pendingRequests.delete(message.id);
                    reject(new Error('Request timeout'));
                }
            }, 60000); // Increased to 60 seconds

            const data = JSON.stringify(message);
            const options = {
                hostname: 'localhost',
                port: this.port,
                path: `/messages?sessionId=${this.sessionId}`,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(data)
                }
            };

            const req = http.request(options, (res) => {
                let body = '';
                res.on('data', (chunk) => body += chunk);
                res.on('end', () => {
                    if (res.statusCode !== 202) {
                        try {
                            const response = JSON.parse(body);
                            this.pendingRequests.delete(message.id);
                            if (response.error) {
                                reject(new Error(response.error.message));
                            } else {
                                resolve(response.result);
                            }
                        } catch (e) {
                            this.pendingRequests.delete(message.id);
                            reject(new Error(`Failed to parse response: ${body}`));
                        }
                    }
                });
            });

            req.on('error', (err) => {
                this.pendingRequests.delete(message.id);
                reject(err);
            });

            req.write(data);
            req.end();
        });
    }

    async initialize() {
        console.log('🚀 Initializing MCP session...');
        return await this.sendMessage('initialize', {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: {
                name: 'figma-to-react-converter',
                version: '1.0.0'
            }
        });
    }

    async getFigmaData(fileKey, nodeId = null) {
        console.log(`📥 Fetching Figma data for file: ${fileKey}`);
        const params = { fileKey };
        if (nodeId) params.nodeId = nodeId;
        
        return await this.sendMessage('tools/call', {
            name: 'get_figma_data',
            arguments: params
        });
    }

    generateReactComponent(figmaData, componentName = 'FigmaComponent') {
        console.log('⚛️  Generating React component...');
        
        // Basic React component template
        let reactCode = `import React from 'react';\n\n`;
        reactCode += `const ${componentName} = () => {\n`;
        reactCode += `  return (\n`;
        reactCode += `    <div className="${componentName.toLowerCase()}">\n`;
        
        // Process Figma data and generate JSX
        if (figmaData && figmaData.document) {
            reactCode += this.processNode(figmaData.document, 6);
        } else {
            reactCode += `      <p>Figma data loaded successfully!</p>\n`;
            reactCode += `      <pre>{JSON.stringify(figmaData, null, 2)}</pre>\n`;
        }
        
        reactCode += `    </div>\n`;
        reactCode += `  );\n`;
        reactCode += `};\n\n`;
        reactCode += `export default ${componentName};\n`;
        
        return reactCode;
    }

    processNode(node, indent = 0) {
        const spaces = ' '.repeat(indent);
        let jsx = '';
        
        if (node.type === 'TEXT') {
            jsx += `${spaces}<span>${node.characters || 'Text'}</span>\n`;
        } else if (node.type === 'RECTANGLE' || node.type === 'FRAME') {
            jsx += `${spaces}<div className="${node.name?.toLowerCase().replace(/\s+/g, '-') || 'element'}">\n`;
            if (node.children) {
                for (const child of node.children) {
                    jsx += this.processNode(child, indent + 2);
                }
            }
            jsx += `${spaces}</div>\n`;
        } else if (node.children) {
            for (const child of node.children) {
                jsx += this.processNode(child, indent);
            }
        }
        
        return jsx;
    }

    saveReactComponent(code, fileName = 'FigmaComponent.jsx') {
        const filePath = path.join(process.cwd(), fileName);
        fs.writeFileSync(filePath, code);
        console.log(`💾 React component saved to: ${filePath}`);
        return filePath;
    }

    disconnect() {
        if (this.sseConnection) {
            this.sseConnection.destroy();
        }
    }
}

// Main execution
async function main() {
    const converter = new FigmaToReactConverter('*********************************************');

    try {
        await converter.connect();
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for session
        
        await converter.initialize();
        console.log('✅ MCP session initialized successfully!');

        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        console.log('\n🎨 Figma to React Converter');
        console.log('Enter a Figma file URL or file key:');
        console.log('Example: https://www.figma.com/file/ABC123/My-Design');
        console.log('Or just: ABC123\n');
        
        rl.question('Figma file: ', async (input) => {
            try {
                // Extract file key from URL if needed
                let fileKey = input.trim();
                const urlMatch = input.match(/figma\.com\/(?:file|design)\/([a-zA-Z0-9]+)/);
                if (urlMatch) {
                    fileKey = urlMatch[1];
                }

                console.log(`\n📋 Using file key: ${fileKey}`);
                
                // Get Figma data
                const figmaData = await converter.getFigmaData(fileKey);
                
                // Generate React component
                const componentName = 'FigmaComponent';
                const reactCode = converter.generateReactComponent(figmaData, componentName);
                
                // Save to file
                const filePath = converter.saveReactComponent(reactCode, `${componentName}.jsx`);
                
                console.log('\n🎉 Conversion completed!');
                console.log(`📁 File saved: ${filePath}`);
                console.log('\n📝 Generated React Component:');
                console.log('─'.repeat(50));
                console.log(reactCode);
                console.log('─'.repeat(50));

            } catch (error) {
                console.error('❌ Error:', error.message);
            } finally {
                converter.disconnect();
                rl.close();
            }
        });

    } catch (error) {
        console.error('❌ Failed to start converter:', error.message);
        converter.disconnect();
    }
}

if (require.main === module) {
    main();
}

module.exports = FigmaToReactConverter;
