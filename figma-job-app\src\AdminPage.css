
.admin-page {
  font-family: 'Inter', 'Segoe UI', sans-serif;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.admin-container {
  display: flex;
  flex: 1;
  min-height: 100vh;
}

/* Left Side - Login Form */
.login-section {
  flex: 1;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-content {
  width: 100%;
  max-width: 400px;
}

.admin-title {
  font-size: 32px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 40px;
  text-align: left;
}

/* Right Side - Branding */
.brand-section {
  flex: 1;
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.brand-content {
  text-align: center;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.spurtree-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.logo-icon {
  width: 80px;
  height: 80px;
}

.logo-svg {
  width: 100%;
  height: 100%;
}

.brand-name {
  color: white;
  font-size: 36px;
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.5px;
}

.admin-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-input {
  padding: 16px 20px;
  border: 2px solid #e5e5e5;
  border-radius: 8px;
  font-size: 16px;
  font-family: 'Inter', sans-serif;
  background: #fafafa;
  transition: all 0.3s ease;
  color: #333;
}

.form-input::placeholder {
  color: #999;
  font-weight: 400;
}

.form-input:focus {
  outline: none;
  border-color: #8B5CF6;
  background: white;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.login-button {
  background: #8B5CF6;
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  font-family: 'Inter', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 8px;
}

.login-button:hover {
  background: #7C3AED;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.login-button:active {
  transform: translateY(0);
}

.figma-credit {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  z-index: 1000;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-container {
    flex-direction: column;
  }

  .brand-section {
    min-height: 200px;
    flex: none;
  }

  .login-section {
    padding: 20px;
  }

  .admin-title {
    font-size: 28px;
    text-align: center;
  }

  .brand-name {
    font-size: 28px;
  }

  .logo-icon {
    width: 60px;
    height: 60px;
  }
}
