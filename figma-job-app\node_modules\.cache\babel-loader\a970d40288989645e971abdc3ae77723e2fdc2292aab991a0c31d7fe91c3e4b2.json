{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\figmaMcp\\\\figma-job-app\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport JobAppComponent from './JobAppComponent';\nimport AdminPage from './AdminPage';\nimport './JobAppComponent.css';\nimport './AdminPage.css';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n        style: {\n          padding: '20px',\n          backgroundColor: '#f0f0f0',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          style: {\n            marginRight: '20px',\n            textDecoration: 'none',\n            color: '#333'\n          },\n          children: \"\\uD83C\\uDFE0 Home (Job App)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/admin\",\n          style: {\n            marginRight: '20px',\n            textDecoration: 'none',\n            color: '#333'\n          },\n          children: \"\\uD83D\\uDD10 Admin Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/about\",\n          style: {\n            marginRight: '20px',\n            textDecoration: 'none',\n            color: '#333'\n          },\n          children: \"\\u2139\\uFE0F About\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#666'\n          },\n          children: \"Add more Figma pages here by running the MCP converter again!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(JobAppComponent, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin\",\n          element: /*#__PURE__*/_jsxDEV(AdminPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/about\",\n          element: /*#__PURE__*/_jsxDEV(AboutPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n}\n\n// Example additional page\n_c = App;\nfunction AboutPage() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      textAlign: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"About This App\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"This app was generated from Figma designs using MCP (Model Context Protocol)!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"To add more pages:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n      style: {\n        textAlign: 'left',\n        maxWidth: '600px',\n        margin: '0 auto'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        children: [\"Run: \", /*#__PURE__*/_jsxDEV(\"code\", {\n          children: \"node figma-to-react-final.js\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 18\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: \"Enter a new Figma file URL\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: \"Copy the generated component to src/\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: \"Add a new Route in App.js\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n}\n_c2 = AboutPage;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"App\");\n$RefreshReg$(_c2, \"AboutPage\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "JobAppComponent", "AdminPage", "jsxDEV", "_jsxDEV", "App", "children", "className", "style", "padding", "backgroundColor", "marginBottom", "to", "marginRight", "textDecoration", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "AboutPage", "_c", "textAlign", "max<PERSON><PERSON><PERSON>", "margin", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/figmaMcp/figma-job-app/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport JobAppComponent from './JobAppComponent';\nimport AdminPage from './AdminPage';\nimport './JobAppComponent.css';\nimport './AdminPage.css';\nimport './App.css';\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"App\">\n        <nav style={{ padding: '20px', backgroundColor: '#f0f0f0', marginBottom: '20px' }}>\n          <Link to=\"/\" style={{ marginRight: '20px', textDecoration: 'none', color: '#333' }}>\n            🏠 Home (Job App)\n          </Link>\n          <Link to=\"/admin\" style={{ marginRight: '20px', textDecoration: 'none', color: '#333' }}>\n            🔐 Admin Login\n          </Link>\n          <Link to=\"/about\" style={{ marginRight: '20px', textDecoration: 'none', color: '#333' }}>\n            ℹ️ About\n          </Link>\n          <span style={{ color: '#666' }}>\n            Add more Figma pages here by running the MCP converter again!\n          </span>\n        </nav>\n\n        <Routes>\n          <Route path=\"/\" element={<JobAppComponent />} />\n          <Route path=\"/admin\" element={<AdminPage />} />\n          <Route path=\"/about\" element={<AboutPage />} />\n          {/* Add more routes here for new Figma components */}\n        </Routes>\n      </div>\n    </Router>\n  );\n}\n\n// Example additional page\nfunction AboutPage() {\n  return (\n    <div style={{ padding: '20px', textAlign: 'center' }}>\n      <h1>About This App</h1>\n      <p>This app was generated from Figma designs using MCP (Model Context Protocol)!</p>\n      <p>To add more pages:</p>\n      <ol style={{ textAlign: 'left', maxWidth: '600px', margin: '0 auto' }}>\n        <li>Run: <code>node figma-to-react-final.js</code></li>\n        <li>Enter a new Figma file URL</li>\n        <li>Copy the generated component to src/</li>\n        <li>Add a new Route in App.js</li>\n      </ol>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,kBAAkB;AAC/E,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAO,uBAAuB;AAC9B,OAAO,iBAAiB;AACxB,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACP,MAAM;IAAAS,QAAA,eACLF,OAAA;MAAKG,SAAS,EAAC,KAAK;MAAAD,QAAA,gBAClBF,OAAA;QAAKI,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,eAAe,EAAE,SAAS;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAL,QAAA,gBAChFF,OAAA,CAACJ,IAAI;UAACY,EAAE,EAAC,GAAG;UAACJ,KAAK,EAAE;YAAEK,WAAW,EAAE,MAAM;YAAEC,cAAc,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAEpF;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPf,OAAA,CAACJ,IAAI;UAACY,EAAE,EAAC,QAAQ;UAACJ,KAAK,EAAE;YAAEK,WAAW,EAAE,MAAM;YAAEC,cAAc,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAEzF;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPf,OAAA,CAACJ,IAAI;UAACY,EAAE,EAAC,QAAQ;UAACJ,KAAK,EAAE;YAAEK,WAAW,EAAE,MAAM;YAAEC,cAAc,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAEzF;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPf,OAAA;UAAMI,KAAK,EAAE;YAAEO,KAAK,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAEhC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENf,OAAA,CAACN,MAAM;QAAAQ,QAAA,gBACLF,OAAA,CAACL,KAAK;UAACqB,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEjB,OAAA,CAACH,eAAe;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDf,OAAA,CAACL,KAAK;UAACqB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEjB,OAAA,CAACF,SAAS;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/Cf,OAAA,CAACL,KAAK;UAACqB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEjB,OAAA,CAACkB,SAAS;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEzC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;;AAEA;AAAAI,EAAA,GA9BSlB,GAAG;AA+BZ,SAASiB,SAASA,CAAA,EAAG;EACnB,oBACElB,OAAA;IAAKI,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEe,SAAS,EAAE;IAAS,CAAE;IAAAlB,QAAA,gBACnDF,OAAA;MAAAE,QAAA,EAAI;IAAc;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACvBf,OAAA;MAAAE,QAAA,EAAG;IAA6E;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eACpFf,OAAA;MAAAE,QAAA,EAAG;IAAkB;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eACzBf,OAAA;MAAII,KAAK,EAAE;QAAEgB,SAAS,EAAE,MAAM;QAAEC,QAAQ,EAAE,OAAO;QAAEC,MAAM,EAAE;MAAS,CAAE;MAAApB,QAAA,gBACpEF,OAAA;QAAAE,QAAA,GAAI,OAAK,eAAAF,OAAA;UAAAE,QAAA,EAAM;QAA4B;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvDf,OAAA;QAAAE,QAAA,EAAI;MAA0B;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCf,OAAA;QAAAE,QAAA,EAAI;MAAoC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7Cf,OAAA;QAAAE,QAAA,EAAI;MAAyB;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEV;AAACQ,GAAA,GAdQL,SAAS;AAgBlB,eAAejB,GAAG;AAAC,IAAAkB,EAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAL,EAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}