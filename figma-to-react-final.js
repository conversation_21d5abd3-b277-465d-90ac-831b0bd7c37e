const http = require('http');
const fs = require('fs');
const path = require('path');

class FigmaToReactFinal {
    constructor(apiKey, port = 3333) {
        this.apiKey = apiKey;
        this.port = port;
        this.messageId = 1;
        this.sessionId = null;
        this.pendingRequests = new Map();
    }

    async connect() {
        return new Promise((resolve, reject) => {
            console.log('🔗 Connecting to Figma MCP Server...');
            
            const options = {
                hostname: 'localhost',
                port: this.port,
                path: '/sse',
                method: 'GET',
                headers: {
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive'
                }
            };

            const req = http.request(options, (res) => {
                let buffer = '';
                res.on('data', (chunk) => {
                    buffer += chunk.toString();
                    
                    // Process complete messages
                    const lines = buffer.split('\n');
                    buffer = lines.pop(); // Keep incomplete line in buffer
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const dataContent = line.substring(6);
                            try {
                                const eventData = JSON.parse(dataContent);
                                this.handleMessage(eventData);
                            } catch (e) {
                                if (dataContent.includes('sessionId=')) {
                                    const match = dataContent.match(/sessionId=([a-f0-9-]+)/);
                                    if (match) {
                                        this.sessionId = match[1];
                                        console.log('✅ Session established:', this.sessionId);
                                    }
                                }
                            }
                        }
                    }
                });

                res.on('error', (err) => reject(err));
                resolve();
            });

            req.on('error', (err) => reject(err));
            req.setTimeout(5000, () => reject(new Error('Connection timeout')));
            req.end();
        });
    }

    handleMessage(message) {
        if (message.id && this.pendingRequests.has(message.id)) {
            const { resolve, reject } = this.pendingRequests.get(message.id);
            this.pendingRequests.delete(message.id);
            
            if (message.error) {
                reject(new Error(message.error.message || 'Unknown error'));
            } else {
                resolve(message.result);
            }
        }
    }

    async sendMessage(method, params = {}) {
        if (!this.sessionId) {
            throw new Error('No session established');
        }

        const message = {
            jsonrpc: '2.0',
            id: this.messageId++,
            method: method,
            params: params
        };

        return new Promise((resolve, reject) => {
            this.pendingRequests.set(message.id, { resolve, reject });
            
            setTimeout(() => {
                if (this.pendingRequests.has(message.id)) {
                    this.pendingRequests.delete(message.id);
                    reject(new Error('Request timeout'));
                }
            }, 120000); // 2 minutes timeout

            const data = JSON.stringify(message);
            const options = {
                hostname: 'localhost',
                port: this.port,
                path: `/messages?sessionId=${this.sessionId}`,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(data)
                }
            };

            const req = http.request(options, (res) => {
                let body = '';
                res.on('data', (chunk) => body += chunk);
                res.on('end', () => {
                    if (res.statusCode !== 202) {
                        try {
                            const response = JSON.parse(body);
                            this.pendingRequests.delete(message.id);
                            if (response.error) {
                                reject(new Error(response.error.message));
                            } else {
                                resolve(response.result);
                            }
                        } catch (e) {
                            this.pendingRequests.delete(message.id);
                            reject(new Error(`Failed to parse response: ${body}`));
                        }
                    }
                });
            });

            req.on('error', (err) => {
                this.pendingRequests.delete(message.id);
                reject(err);
            });

            req.write(data);
            req.end();
        });
    }

    async initialize() {
        console.log('🚀 Initializing MCP session...');
        return await this.sendMessage('initialize', {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: {
                name: 'figma-to-react-final',
                version: '1.0.0'
            }
        });
    }

    async getFigmaData(fileKey, nodeId = null) {
        if (nodeId) {
            console.log(`📥 Fetching Figma data for file: ${fileKey}, node: ${nodeId}`);
        } else {
            console.log(`📥 Fetching Figma data for file: ${fileKey}`);
        }

        const params = { fileKey };
        if (nodeId) {
            params.nodeId = nodeId;
        }

        return await this.sendMessage('tools/call', {
            name: 'get_figma_data',
            arguments: params
        });
    }

    parseYamlContent(yamlText) {
        // Simple YAML parsing for our specific structure
        const lines = yamlText.split('\n');
        const result = { nodes: [], metadata: {} };
        
        let currentSection = null;
        let currentNode = null;
        
        for (const line of lines) {
            const trimmed = line.trim();
            
            if (trimmed.startsWith('metadata:')) {
                currentSection = 'metadata';
            } else if (trimmed.startsWith('nodes:')) {
                currentSection = 'nodes';
            } else if (currentSection === 'nodes' && trimmed.startsWith('- id:')) {
                if (currentNode) result.nodes.push(currentNode);
                currentNode = { id: trimmed.split("'")[1] };
            } else if (currentNode && trimmed.startsWith('name:')) {
                currentNode.name = trimmed.split(': ')[1];
            } else if (currentNode && trimmed.startsWith('type:')) {
                currentNode.type = trimmed.split(': ')[1];
            }
        }
        
        if (currentNode) result.nodes.push(currentNode);
        return result;
    }

    generateReactComponent(figmaData, componentName = 'FigmaComponent') {
        console.log('⚛️  Generating React component...');
        
        // Extract YAML content from the response
        let yamlContent = '';
        if (figmaData.content && figmaData.content[0] && figmaData.content[0].text) {
            yamlContent = figmaData.content[0].text;
        }
        
        // Parse the YAML to extract meaningful structure
        const parsedData = this.parseYamlContent(yamlContent);
        
        let reactCode = `import React from 'react';\n\n`;
        reactCode += `const ${componentName} = () => {\n`;
        reactCode += `  return (\n`;
        reactCode += `    <div className="${componentName.toLowerCase()}">\n`;
        reactCode += `      <h1>Figma Design: Job App</h1>\n`;
        
        // Generate components based on parsed nodes
        const frames = parsedData.nodes.filter(node => node.type === 'FRAME');
        
        for (const frame of frames.slice(0, 5)) { // Limit to first 5 frames
            const frameName = frame.name || 'Unnamed Frame';
            const className = frameName.toLowerCase().replace(/\s+/g, '-');
            
            reactCode += `      <div className="frame ${className}">\n`;
            reactCode += `        <h2>${frameName}</h2>\n`;
            reactCode += `        {/* Frame ID: ${frame.id} */}\n`;
            reactCode += `      </div>\n`;
        }
        
        reactCode += `    </div>\n`;
        reactCode += `  );\n`;
        reactCode += `};\n\n`;
        reactCode += `export default ${componentName};\n`;
        
        return reactCode;
    }

    generateCSS(componentName = 'FigmaComponent') {
        return `
.${componentName.toLowerCase()} {
  font-family: 'Montserrat', sans-serif;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.frame {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f9f9f9;
}

.frame h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 18px;
}

.sign-up, .login, .admin {
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.after-applying-to-a-job {
  background: #f0f8ff;
}

.homescreen {
  background: #fff8f0;
}
`;
    }

    saveFiles(reactCode, cssCode, componentName = 'FigmaComponent') {
        const jsxPath = path.join(process.cwd(), `${componentName}.jsx`);
        const cssPath = path.join(process.cwd(), `${componentName}.css`);
        
        fs.writeFileSync(jsxPath, reactCode);
        fs.writeFileSync(cssPath, cssCode);
        
        console.log(`💾 React component saved to: ${jsxPath}`);
        console.log(`💾 CSS styles saved to: ${cssPath}`);
        
        return { jsxPath, cssPath };
    }
}

// Main execution
async function main() {
    const converter = new FigmaToReactFinal();

    try {
        await converter.connect();
        await new Promise(resolve => setTimeout(resolve, 3000)); // Wait for session
        
        await converter.initialize();
        console.log('✅ MCP session initialized successfully!');

        // Use your specific URL with node ID
        const figmaUrl = 'https://www.figma.com/design/Py8RFAyWK8jUklLH9fqHvo/job-app?node-id=109-118&t=iuTeasJladKB4SAW-4';

        // Extract file key and node ID
        const urlMatch = figmaUrl.match(/figma\.com\/(?:file|design)\/([a-zA-Z0-9]+)/);
        const nodeMatch = figmaUrl.match(/node-id=([0-9-]+)/);

        const fileKey = urlMatch ? urlMatch[1] : 'Py8RFAyWK8jUklLH9fqHvo';
        const nodeId = nodeMatch ? nodeMatch[1] : null;

        console.log(`\n📋 Converting Figma file: ${fileKey}`);
        if (nodeId) {
            console.log(`🎯 Targeting specific node: ${nodeId}`);
        }

        // Get Figma data
        const figmaData = await converter.getFigmaData(fileKey, nodeId);
        console.log('✅ Figma data received successfully!');
        
        // Generate React component
        const componentName = nodeId ? `JobAppPage_${nodeId.replace('-', '_')}` : 'JobAppComponent';
        const reactCode = converter.generateReactComponent(figmaData, componentName);
        const cssCode = converter.generateCSS(componentName);
        
        // Save files
        const { jsxPath, cssPath } = converter.saveFiles(reactCode, cssCode, componentName);
        
        console.log('\n🎉 Conversion completed successfully!');
        console.log(`📁 Files created:`);
        console.log(`   - ${jsxPath}`);
        console.log(`   - ${cssPath}`);
        
        console.log('\n📝 Generated React Component:');
        console.log('─'.repeat(60));
        console.log(reactCode);
        console.log('─'.repeat(60));

    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

if (require.main === module) {
    main();
}

module.exports = FigmaToReactFinal;
