import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import JobAppComponent from './JobAppComponent';
import './JobAppComponent.css';
import './App.css';

function App() {
  return (
    <Router>
      <div className="App">
        <nav style={{ padding: '20px', backgroundColor: '#f0f0f0', marginBottom: '20px' }}>
          <Link to="/" style={{ marginRight: '20px', textDecoration: 'none', color: '#333' }}>
            🏠 Home (Job App)
          </Link>
          <Link to="/about" style={{ marginRight: '20px', textDecoration: 'none', color: '#333' }}>
            ℹ️ About
          </Link>
          <span style={{ color: '#666' }}>
            Add more Figma pages here by running the MCP converter again!
          </span>
        </nav>

        <Routes>
          <Route path="/" element={<JobAppComponent />} />
          <Route path="/about" element={<AboutPage />} />
          {/* Add more routes here for new Figma components */}
        </Routes>
      </div>
    </Router>
  );
}

// Example additional page
function AboutPage() {
  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <h1>About This App</h1>
      <p>This app was generated from Figma designs using MCP (Model Context Protocol)!</p>
      <p>To add more pages:</p>
      <ol style={{ textAlign: 'left', maxWidth: '600px', margin: '0 auto' }}>
        <li>Run: <code>node figma-to-react-final.js</code></li>
        <li>Enter a new Figma file URL</li>
        <li>Copy the generated component to src/</li>
        <li>Add a new Route in App.js</li>
      </ol>
    </div>
  );
}

export default App;
