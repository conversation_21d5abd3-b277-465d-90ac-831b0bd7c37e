import React from 'react';

const AdminPage = () => {
  return (
    <div className="admin-page">
      <h1>Admin Login Page</h1>
      <p>🎯 This page was generated from Figma node: 109-118</p>

      {/* Admin Login Form */}
      <div className="admin-login-container">
        <div className="admin-header">
          <h2>Admin LogIn</h2>
        </div>

        <form className="admin-form">
          <div className="form-group">
            <label htmlFor="username">Username</label>
            <input
              type="text"
              id="username"
              name="username"
              placeholder="Enter username"
              className="form-input"
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              name="password"
              placeholder="Enter password"
              className="form-input"
            />
          </div>

          <button type="submit" className="login-button">
            Log In
          </button>
        </form>

        <div className="admin-logo">
          {/* Placeholder for admin logo */}
          <div className="logo-placeholder">Admin Portal</div>
        </div>
      </div>
    </div>
  );
};

export default AdminPage;
