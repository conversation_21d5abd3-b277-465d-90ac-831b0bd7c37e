import React from 'react';

const JobAppComponent = () => {
  return (
    <div className="jobappcomponent">
      <h1>Figma Design: Job App</h1>
      <div className="frame sign-up">
        <h2>Sign Up</h2>
        {/* Frame ID: 109:89 */}
      </div>
      <div className="frame login">
        <h2>LogIn</h2>
        {/* Frame ID: 109:107 */}
      </div>
      <div className="frame admin">
        <h2>Admin</h2>
        {/* Frame ID: 109:118 */}
      </div>
      <div className="frame admin">
        <h2>Admin</h2>
        {/* Frame ID: 109:132 */}
      </div>
      <div className="frame after-applying-to-a-job">
        <h2>After applying to a job</h2>
        {/* Frame ID: 109:172 */}
      </div>
    </div>
  );
};

export default JobAppComponent;
