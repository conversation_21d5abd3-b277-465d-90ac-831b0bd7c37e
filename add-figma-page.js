const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Helper script to add new Figma pages to your React app
class FigmaPageAdder {
    constructor() {
        this.reactAppPath = './figma-job-app/src';
        this.appJsPath = './figma-job-app/src/App.js';
    }

    async addNewPage() {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        console.log('🎨 Add New Figma Page to React App');
        console.log('=====================================\n');

        // Get component name
        const componentName = await this.askQuestion(rl, 'Enter component name (e.g., "Dashboard"): ');
        const routePath = await this.askQuestion(rl, `Enter route path (e.g., "/dashboard"): `);
        
        console.log(`\n📋 Summary:`);
        console.log(`   Component: ${componentName}`);
        console.log(`   Route: ${routePath}`);
        console.log(`   Files expected: ${componentName}.jsx, ${componentName}.css`);

        const confirm = await this.askQuestion(rl, '\nProceed? (y/n): ');
        
        if (confirm.toLowerCase() !== 'y') {
            console.log('❌ Cancelled');
            rl.close();
            return;
        }

        // Check if component files exist
        const jsxFile = path.join(this.reactAppPath, `${componentName}.jsx`);
        const cssFile = path.join(this.reactAppPath, `${componentName}.css`);

        if (!fs.existsSync(jsxFile)) {
            console.log(`❌ Component file not found: ${jsxFile}`);
            console.log(`\n💡 To create this component:`);
            console.log(`   1. Run: node figma-to-react-final.js`);
            console.log(`   2. Enter your Figma file URL`);
            console.log(`   3. Rename generated files to ${componentName}.jsx and ${componentName}.css`);
            console.log(`   4. Copy them to figma-job-app/src/`);
            console.log(`   5. Run this script again`);
            rl.close();
            return;
        }

        // Add route to App.js
        try {
            await this.addRouteToApp(componentName, routePath);
            console.log(`✅ Successfully added ${componentName} to your React app!`);
            console.log(`🌐 Visit: http://localhost:3000${routePath}`);
        } catch (error) {
            console.error(`❌ Error adding route: ${error.message}`);
        }

        rl.close();
    }

    async addRouteToApp(componentName, routePath) {
        const appJsContent = fs.readFileSync(this.appJsPath, 'utf8');
        
        // Add import
        const importLine = `import ${componentName} from './${componentName}';`;
        const cssImportLine = `import './${componentName}.css';`;
        
        let updatedContent = appJsContent;
        
        // Add imports after existing imports
        const lastImportIndex = updatedContent.lastIndexOf("import");
        const nextLineIndex = updatedContent.indexOf('\n', lastImportIndex);
        
        updatedContent = updatedContent.slice(0, nextLineIndex + 1) + 
                        importLine + '\n' + 
                        cssImportLine + '\n' + 
                        updatedContent.slice(nextLineIndex + 1);

        // Add navigation link
        const navLinkToAdd = `          <Link to="${routePath}" style={{ marginRight: '20px', textDecoration: 'none', color: '#333' }}>
            📄 ${componentName}
          </Link>`;
        
        const aboutLinkIndex = updatedContent.indexOf('<Link to="/about"');
        if (aboutLinkIndex !== -1) {
            updatedContent = updatedContent.slice(0, aboutLinkIndex) + 
                           navLinkToAdd + '\n          ' +
                           updatedContent.slice(aboutLinkIndex);
        }

        // Add route
        const routeToAdd = `          <Route path="${routePath}" element={<${componentName} />} />`;
        const aboutRouteIndex = updatedContent.indexOf('<Route path="/about"');
        if (aboutRouteIndex !== -1) {
            updatedContent = updatedContent.slice(0, aboutRouteIndex) + 
                           routeToAdd + '\n          ' +
                           updatedContent.slice(aboutRouteIndex);
        }

        fs.writeFileSync(this.appJsPath, updatedContent);
    }

    askQuestion(rl, question) {
        return new Promise((resolve) => {
            rl.question(question, (answer) => {
                resolve(answer.trim());
            });
        });
    }
}

// Usage instructions
function showUsage() {
    console.log(`
🎨 Figma to React - Add New Page Helper
======================================

This script helps you add new Figma-generated components to your React app.

STEPS TO ADD A NEW PAGE:

1. 📥 Generate component from Figma:
   node figma-to-react-final.js
   
2. 📁 Copy generated files to React app:
   copy NewComponent.jsx figma-job-app\\src\\
   copy NewComponent.css figma-job-app\\src\\
   
3. 🔗 Add to React app:
   node add-figma-page.js

EXAMPLE WORKFLOW:
- Generate "Dashboard" component from Figma
- Copy Dashboard.jsx and Dashboard.css to src/
- Run this script to add routing
- Visit http://localhost:3000/dashboard

Ready to add a page? Run: node add-figma-page.js
`);
}

// Main execution
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        showUsage();
    } else {
        const adder = new FigmaPageAdder();
        adder.addNewPage().catch(console.error);
    }
}

module.exports = FigmaPageAdder;
