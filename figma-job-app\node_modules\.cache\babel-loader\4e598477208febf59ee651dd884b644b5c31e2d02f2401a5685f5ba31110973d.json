{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\figmaMcp\\\\figma-job-app\\\\src\\\\AdminPage.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Admin Login Page\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"\\uD83C\\uDFAF This page was generated from Figma node: 109-118\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-login-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Admin LogIn\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"admin-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"username\",\n            children: \"Username\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"username\",\n            name: \"username\",\n            placeholder: \"Enter username\",\n            className: \"form-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"password\",\n            name: \"password\",\n            placeholder: \"Enter password\",\n            className: \"form-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"login-button\",\n          children: \"Log In\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-logo\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-placeholder\",\n          children: \"Admin Portal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = AdminPage;\nexport default AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "AdminPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "type", "id", "name", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/figmaMcp/figma-job-app/src/AdminPage.jsx"], "sourcesContent": ["import React from 'react';\n\nconst AdminPage = () => {\n  return (\n    <div className=\"admin-page\">\n      <h1>Admin Login Page</h1>\n      <p>🎯 This page was generated from Figma node: 109-118</p>\n\n      {/* Admin Login Form */}\n      <div className=\"admin-login-container\">\n        <div className=\"admin-header\">\n          <h2>Admin LogIn</h2>\n        </div>\n\n        <form className=\"admin-form\">\n          <div className=\"form-group\">\n            <label htmlFor=\"username\">Username</label>\n            <input\n              type=\"text\"\n              id=\"username\"\n              name=\"username\"\n              placeholder=\"Enter username\"\n              className=\"form-input\"\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"password\">Password</label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              placeholder=\"Enter password\"\n              className=\"form-input\"\n            />\n          </div>\n\n          <button type=\"submit\" className=\"login-button\">\n            Log In\n          </button>\n        </form>\n\n        <div className=\"admin-logo\">\n          {/* Placeholder for admin logo */}\n          <div className=\"logo-placeholder\">Admin Portal</div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtB,oBACED,OAAA;IAAKE,SAAS,EAAC,YAAY;IAAAC,QAAA,gBACzBH,OAAA;MAAAG,QAAA,EAAI;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACzBP,OAAA;MAAAG,QAAA,EAAG;IAAmD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAG1DP,OAAA;MAAKE,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBACpCH,OAAA;QAAKE,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BH,OAAA;UAAAG,QAAA,EAAI;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAENP,OAAA;QAAME,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAC1BH,OAAA;UAAKE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBH,OAAA;YAAOQ,OAAO,EAAC,UAAU;YAAAL,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1CP,OAAA;YACES,IAAI,EAAC,MAAM;YACXC,EAAE,EAAC,UAAU;YACbC,IAAI,EAAC,UAAU;YACfC,WAAW,EAAC,gBAAgB;YAC5BV,SAAS,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENP,OAAA;UAAKE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBH,OAAA;YAAOQ,OAAO,EAAC,UAAU;YAAAL,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1CP,OAAA;YACES,IAAI,EAAC,UAAU;YACfC,EAAE,EAAC,UAAU;YACbC,IAAI,EAAC,UAAU;YACfC,WAAW,EAAC,gBAAgB;YAC5BV,SAAS,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENP,OAAA;UAAQS,IAAI,EAAC,QAAQ;UAACP,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAE/C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPP,OAAA;QAAKE,SAAS,EAAC,YAAY;QAAAC,QAAA,eAEzBH,OAAA;UAAKE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACM,EAAA,GA/CIZ,SAAS;AAiDf,eAAeA,SAAS;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}