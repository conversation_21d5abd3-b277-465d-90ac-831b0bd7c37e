[{"C:\\Users\\<USER>\\Documents\\figmaMcp\\figma-job-app\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\figmaMcp\\figma-job-app\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\figmaMcp\\figma-job-app\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\figmaMcp\\figma-job-app\\src\\JobAppComponent.jsx": "4"}, {"size": 535, "mtime": 1752140623639, "results": "5", "hashOfConfig": "6"}, {"size": 1668, "mtime": 1752141048353, "results": "7", "hashOfConfig": "6"}, {"size": 362, "mtime": 1752140623979, "results": "8", "hashOfConfig": "6"}, {"size": 767, "mtime": 1752140256685, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1jv3uib", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\figmaMcp\\figma-job-app\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\figmaMcp\\figma-job-app\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\figmaMcp\\figma-job-app\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\figmaMcp\\figma-job-app\\src\\JobAppComponent.jsx", [], []]