[{"C:\\Users\\<USER>\\Documents\\figmaMcp\\figma-job-app\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\figmaMcp\\figma-job-app\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\figmaMcp\\figma-job-app\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\figmaMcp\\figma-job-app\\src\\JobAppComponent.jsx": "4", "C:\\Users\\<USER>\\Documents\\figmaMcp\\figma-job-app\\src\\AdminPage.jsx": "5"}, {"size": 535, "mtime": 1752140623639, "results": "6", "hashOfConfig": "7"}, {"size": 1936, "mtime": 1752141507491, "results": "8", "hashOfConfig": "7"}, {"size": 362, "mtime": 1752140623979, "results": "9", "hashOfConfig": "7"}, {"size": 767, "mtime": 1752140256685, "results": "10", "hashOfConfig": "7"}, {"size": 1356, "mtime": 1752141413602, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1jv3uib", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\figmaMcp\\figma-job-app\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\figmaMcp\\figma-job-app\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\figmaMcp\\figma-job-app\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\figmaMcp\\figma-job-app\\src\\JobAppComponent.jsx", [], [], "C:\\Users\\<USER>\\Documents\\figmaMcp\\figma-job-app\\src\\AdminPage.jsx", [], []]