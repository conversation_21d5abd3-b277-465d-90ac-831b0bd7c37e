const http = require('http');
const readline = require('readline');

class FigmaSSEClient {
    constructor(apiKey, port = 3333) {
        this.apiKey = apiKey;
        this.port = port;
        this.messageId = 1;
        this.sessionId = null;
        this.sseConnection = null;
        this.pendingRequests = new Map(); // Track pending requests by ID
    }

    async connect() {
        return new Promise((resolve, reject) => {
            console.log('Connecting to Figma MCP Server via SSE...');
            
            const options = {
                hostname: 'localhost',
                port: this.port,
                path: '/sse',
                method: 'GET',
                headers: {
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive'
                }
            };

            const req = http.request(options, (res) => {
                console.log('SSE connection established, status:', res.statusCode);
                
                // Extract session ID from response headers if available
                if (res.headers['x-session-id']) {
                    this.sessionId = res.headers['x-session-id'];
                    console.log('Session ID:', this.sessionId);
                }

                this.sseConnection = res;
                
                res.on('data', (chunk) => {
                    const data = chunk.toString();
                    console.log('SSE Data received:', data);
                    
                    // Parse SSE events
                    const lines = data.split('\n');
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const dataContent = line.substring(6);
                            try {
                                const eventData = JSON.parse(dataContent);
                                this.handleMessage(eventData);
                            } catch (e) {
                                // Check if it's a session endpoint URL
                                if (dataContent.includes('sessionId=')) {
                                    const match = dataContent.match(/sessionId=([a-f0-9-]+)/);
                                    if (match) {
                                        this.sessionId = match[1];
                                        console.log('Extracted session ID:', this.sessionId);
                                    }
                                } else {
                                    console.log('Non-JSON SSE data:', line);
                                }
                            }
                        }
                    }
                });

                res.on('end', () => {
                    console.log('SSE connection ended');
                });

                res.on('error', (err) => {
                    console.error('SSE error:', err);
                    reject(err);
                });

                resolve();
            });

            req.on('error', (err) => {
                console.error('Connection error:', err);
                reject(err);
            });

            req.setTimeout(5000, () => {
                reject(new Error('Connection timeout'));
            });

            req.end();
        });
    }

    handleMessage(message) {
        console.log('Received message:', JSON.stringify(message, null, 2));

        // Check if this is a response to a pending request
        if (message.id && this.pendingRequests.has(message.id)) {
            const { resolve, reject } = this.pendingRequests.get(message.id);
            this.pendingRequests.delete(message.id);

            if (message.error) {
                reject(new Error(message.error.message || 'Unknown error'));
            } else {
                resolve(message.result);
            }
        }
    }

    async sendMessage(method, params = {}) {
        if (!this.sessionId) {
            throw new Error('No session established');
        }

        const message = {
            jsonrpc: '2.0',
            id: this.messageId++,
            method: method,
            params: params
        };

        return new Promise((resolve, reject) => {
            // Store the promise resolvers for this request ID
            this.pendingRequests.set(message.id, { resolve, reject });

            // Set timeout for the request
            setTimeout(() => {
                if (this.pendingRequests.has(message.id)) {
                    this.pendingRequests.delete(message.id);
                    reject(new Error('Request timeout'));
                }
            }, 30000);

            const data = JSON.stringify(message);

            const options = {
                hostname: 'localhost',
                port: this.port,
                path: `/messages?sessionId=${this.sessionId}`,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(data)
                }
            };

            const req = http.request(options, (res) => {
                let body = '';
                res.on('data', (chunk) => {
                    body += chunk;
                });
                res.on('end', () => {
                    console.log('POST Response status:', res.statusCode);
                    console.log('POST Response body:', body);

                    // For 202 Accepted, we wait for the response via SSE
                    if (res.statusCode === 202) {
                        console.log('Message accepted, waiting for response via SSE...');
                        return;
                    }

                    // Handle immediate responses
                    try {
                        const response = JSON.parse(body);
                        this.pendingRequests.delete(message.id);
                        if (response.error) {
                            reject(new Error(response.error.message || 'Unknown error'));
                        } else {
                            resolve(response.result);
                        }
                    } catch (e) {
                        this.pendingRequests.delete(message.id);
                        reject(new Error(`Failed to parse response: ${body}`));
                    }
                });
            });

            req.on('error', (err) => {
                this.pendingRequests.delete(message.id);
                reject(err);
            });

            req.write(data);
            req.end();
        });
    }

    async initialize() {
        return await this.sendMessage('initialize', {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: {
                name: 'figma-to-react-client',
                version: '1.0.0'
            }
        });
    }

    async listTools() {
        return await this.sendMessage('tools/list');
    }

    async callTool(name, arguments_obj) {
        return await this.sendMessage('tools/call', {
            name: name,
            arguments: arguments_obj
        });
    }

    disconnect() {
        if (this.sseConnection) {
            this.sseConnection.destroy();
        }
    }
}

// Test the client
async function main() {
    const client = new FigmaSSEClient('*********************************************');

    try {
        await client.connect();
        
        // Wait a moment for connection to stabilize
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        console.log('\nInitializing MCP session...');
        const initResult = await client.initialize();
        console.log('Initialize result:', initResult);

        console.log('\nListing available tools...');
        const tools = await client.listTools();
        console.log('Available tools:', JSON.stringify(tools, null, 2));

        // Interactive prompt for Figma file
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        rl.question('\nEnter Figma file ID to convert: ', async (fileId) => {
            try {
                console.log(`\nCalling get_figma_file with ID: ${fileId}`);
                const fileData = await client.callTool('get_figma_file', { file_id: fileId });
                console.log('File data:', JSON.stringify(fileData, null, 2));
            } catch (error) {
                console.error('Error calling tool:', error.message);
            } finally {
                client.disconnect();
                rl.close();
            }
        });

    } catch (error) {
        console.error('Client error:', error.message);
        client.disconnect();
    }
}

if (require.main === module) {
    main();
}

module.exports = FigmaSSEClient;
